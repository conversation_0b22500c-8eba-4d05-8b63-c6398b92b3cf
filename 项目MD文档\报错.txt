Microsoft Windows [版本 10.0.26100.4349]
(c) Microsoft Corporation。保留所有权利。

C:\Users\<USER>\Desktop\work\flyXxtt2>composer install
Installing dependencies from lock file (including require-dev)
Verifying lock file contents can be installed on current platform.
Your lock file does not contain a compatible set of packages. Please run composer update.

  Problem 1
    - laravel/horizon is locked to version v5.24.5 and an update of this package was not requested.
    - laravel/horizon v5.24.5 requires ext-pcntl * -> it is missing from your system. Install or enable PHP's pcntl extension.
  Problem 2
    - openspout/openspout is locked to version v4.23.0 and an update of this package was not requested.
    - openspout/openspout v4.23.0 requires ext-zip * -> it is missing from your system. Install or enable P<PERSON>'s zip extension.
  Problem 3
    - filament/actions is locked to version v3.2.6 and an update of this package was not requested.
    - filament/actions v3.2.6 requires openspout/openspout ^4.23 -> satisfiable by openspout/openspout[v4.23.0].
    - openspout/openspout v4.23.0 requires ext-zip * -> it is missing from your system. Install or enable PHP's zip extension.

To enable extensions, verify that they are enabled in your .ini files:
    - C:\phpstudy_pro\Extensions\php\php8.2.9nts\php.ini
You can also run `php --ini` in a terminal to see which files are used by PHP in CLI mode.
Alternatively, you can run Composer with `--ignore-platform-req=ext-pcntl --ignore-platform-req=ext-zip` to temporarily ignore these required extensions.

C:\Users\<USER>\Desktop\work\flyXxtt2>
