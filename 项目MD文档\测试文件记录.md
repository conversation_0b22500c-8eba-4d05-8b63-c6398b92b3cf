# XXTT2 项目测试文件记录

## 📋 测试文件管理规范

### 🎯 设计原则
- **环境隔离**: 所有测试功能仅在 `APP_ENV=local` 环境下可用
- **安全性**: 生产环境自动禁用所有测试路由和功能
- **规范化**: 统一管理和记录所有测试文件
- **可维护性**: 清晰的文件结构和文档说明

---

## 🗂️ 测试文件清单

### 1. 数据库连接测试

#### 📁 文件位置
- **控制器**: `app/Http/Controllers/DatabaseTestController.php`
- **视图**: `resources/views/database-test.blade.php`
- **路由**: `routes/web.php` (Line 20)

#### 🌐 访问地址
```
http://localhost/db-test
```

#### 🔧 功能描述
- 测试所有数据库连接状态 (CMS、Redis、游戏服务器)
- 显示连接详细信息和版本
- 实时统计数据展示
- 环境安全检查 (仅local环境可用)

#### 📊 测试内容
- **Web CMS数据库** (mysql连接)
  - 连接状态检测
  - 用户数量统计
  - 新闻数量统计
  - 订单数量统计

- **Redis缓存数据库**
  - 连接状态检测
  - 读写功能测试
  - 配置信息显示

- **游戏服务器数据库** (仅当 `GAME_SERVER_ENABLED=true`)
  - lin2world (游戏世界数据)
  - lin2db (用户账户数据)
  - 角色和账户数量统计

#### 🛡️ 安全措施
```php
// 环境检查代码
if (config('app.env') !== 'local') {
    abort(404, '测试页面仅在开发环境中可用');
}
```

#### 📝 使用说明
1. 确保 `.env` 文件中 `APP_ENV=local`
2. 访问 `http://localhost/db-test`
3. 查看所有数据库连接状态
4. 检查统计数据是否正常

---

## 🔄 环境切换说明

### 开发环境 (APP_ENV=local)
```env
APP_ENV=local
APP_DEBUG=true
```
- ✅ 所有测试功能可用
- ✅ 详细错误信息显示
- ✅ 调试工具启用

### 生产环境 (APP_ENV=production)
```env
APP_ENV=production
APP_DEBUG=false
```
- ❌ 测试功能自动禁用
- ❌ 测试路由返回404
- ✅ 安全性最大化

---

## 📋 测试文件开发规范

### 1. 控制器规范
```php
<?php
namespace App\Http\Controllers;

class TestController extends Controller
{
    public function index()
    {
        // 必须添加环境检查
        if (config('app.env') !== 'local') {
            abort(404, '测试页面仅在开发环境中可用');
        }

        // 测试逻辑...
    }
}
```

### 2. 路由规范
```php
// 在 routes/web.php 中添加注释
// 测试路由 - 仅开发环境可用
Route::get('/test-name', [TestController::class, 'index'])->name('test-name');
```

### 3. 视图规范
- 文件命名: `test-功能名.blade.php`
- 统一样式风格
- 包含环境标识
- 添加刷新和返回功能

### 4. 文档规范
- 每个测试功能必须在此文档中记录
- 包含文件位置、访问地址、功能描述
- 更新时间和维护者信息

---

## 🚀 未来测试功能规划

### 计划添加的测试功能

#### 2. API接口测试
- **路径**: `/api-test`
- **功能**: 测试所有API端点
- **状态**: 📋 计划中

#### 3. 邮件系统测试
- **路径**: `/mail-test`
- **功能**: 测试邮件发送功能
- **状态**: 📋 计划中

#### 4. 缓存系统测试
- **路径**: `/cache-test`
- **功能**: 测试Redis缓存性能
- **状态**: 📋 计划中

#### 5. 队列系统测试
- **路径**: `/queue-test`
- **功能**: 测试任务队列处理
- **状态**: 📋 计划中

#### 6. 文件上传测试
- **路径**: `/upload-test`
- **功能**: 测试文件上传功能
- **状态**: 📋 计划中

---

## 📝 维护日志

### 2025-07-30
- ✅ 创建测试文件记录文档
- ✅ 实现数据库连接测试功能
- ✅ 添加环境安全检查机制
- ✅ 建立测试文件开发规范

### 维护者
- **创建者**: AI助手
- **项目负责人**: Java程序员
- **最后更新**: 2025-07-30

---

## 🔍 故障排除

### 常见问题

#### 1. 测试页面显示404
**原因**: 环境不是local或路由未正确配置
**解决**:
```bash
# 检查环境配置
grep APP_ENV .env

# 检查路由
docker-compose exec laravel.test php artisan route:list --name=db-test
```

#### 2. 数据库连接失败
**原因**: 数据库服务未启动或配置错误
**解决**:
```bash
# 检查容器状态
docker-compose ps

# 重启数据库服务
docker-compose restart mysql
```

#### 3. 游戏数据库无法连接
**原因**: 网络问题或游戏服务器配置错误
**解决**:
```bash
# 检查网络连通性
ping 103.88.35.205

# 检查配置
grep GAME_SERVER_ENABLED .env
```

---

*文档创建时间: 2025-07-30*
*遵循项目最佳实践和安全规范*
