<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Exception;

class DatabaseTestController extends Controller
{
    public function index()
    {
        $connections = [];
        
        // 测试主数据库连接 (Web CMS)
        $connections['cms'] = $this->testConnection('mysql', 'Web CMS数据库 (lin2web_cms)');
        
        // 测试Redis连接
        $connections['redis'] = $this->testRedisConnection();
        
        // 测试游戏服务器数据库连接
        if (config('app.l2server_type') === 'pts') {
            $connections['lin2world'] = $this->testConnection('lin2world', '游戏世界数据库 (lin2world)');
            $connections['lin2db'] = $this->testConnection('lin2db', '用户账户数据库 (lin2db)');
        }
        
        // 获取一些统计数据
        $stats = $this->getStatistics();
        
        return view('database-test', compact('connections', 'stats'));
    }
    
    private function testConnection($connectionName, $description)
    {
        try {
            $pdo = DB::connection($connectionName)->getPdo();
            
            // 测试简单查询
            $result = DB::connection($connectionName)->select('SELECT 1 as test');
            
            // 获取数据库信息
            $dbInfo = $this->getDatabaseInfo($connectionName);
            
            return [
                'name' => $connectionName,
                'description' => $description,
                'status' => 'success',
                'message' => '连接成功',
                'info' => $dbInfo,
                'test_result' => $result[0]->test ?? null
            ];
        } catch (Exception $e) {
            return [
                'name' => $connectionName,
                'description' => $description,
                'status' => 'error',
                'message' => '连接失败: ' . $e->getMessage(),
                'info' => null,
                'test_result' => null
            ];
        }
    }
    
    private function testRedisConnection()
    {
        try {
            // 测试Redis连接
            $redis = Redis::connection();
            $redis->ping();
            
            // 测试写入和读取
            $testKey = 'db_test_' . time();
            $redis->set($testKey, 'test_value', 'EX', 60);
            $testValue = $redis->get($testKey);
            $redis->del($testKey);
            
            return [
                'name' => 'redis',
                'description' => 'Redis缓存数据库',
                'status' => 'success',
                'message' => '连接成功',
                'info' => [
                    'host' => config('database.redis.default.host'),
                    'port' => config('database.redis.default.port'),
                    'test_write_read' => $testValue === 'test_value' ? '读写测试通过' : '读写测试失败'
                ],
                'test_result' => 'PONG'
            ];
        } catch (Exception $e) {
            return [
                'name' => 'redis',
                'description' => 'Redis缓存数据库',
                'status' => 'error',
                'message' => '连接失败: ' . $e->getMessage(),
                'info' => null,
                'test_result' => null
            ];
        }
    }
    
    private function getDatabaseInfo($connectionName)
    {
        try {
            $config = config("database.connections.{$connectionName}");
            
            if ($connectionName === 'mysql') {
                // MySQL数据库信息
                $version = DB::connection($connectionName)->select('SELECT VERSION() as version')[0]->version;
                $database = $config['database'];
                
                return [
                    'driver' => $config['driver'],
                    'host' => $config['host'],
                    'port' => $config['port'],
                    'database' => $database,
                    'version' => $version
                ];
            } elseif (in_array($connectionName, ['lin2world', 'lin2db'])) {
                // SQL Server数据库信息
                $version = DB::connection($connectionName)->select('SELECT @@VERSION as version')[0]->version;
                
                return [
                    'driver' => $config['driver'],
                    'host' => $config['host'],
                    'port' => $config['port'],
                    'database' => $config['database'],
                    'version' => substr($version, 0, 100) . '...'
                ];
            }
            
            return $config;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    private function getStatistics()
    {
        $stats = [];
        
        try {
            // CMS数据库统计
            $stats['cms'] = [
                'users_count' => DB::connection('mysql')->table('users')->count(),
                'news_count' => DB::connection('mysql')->table('news')->count(),
                'orders_count' => DB::connection('mysql')->table('donate_orders')->count(),
            ];
        } catch (Exception $e) {
            $stats['cms'] = ['error' => $e->getMessage()];
        }
        
        // 游戏数据库统计
        if (config('app.l2server_type') === 'pts') {
            try {
                $stats['game'] = [
                    'characters_count' => DB::connection('lin2world')->table('user_data')->count(),
                    'accounts_count' => DB::connection('lin2db')->table('user_account')->count(),
                ];
            } catch (Exception $e) {
                $stats['game'] = ['error' => $e->getMessage()];
            }
        }
        
        return $stats;
    }
}
