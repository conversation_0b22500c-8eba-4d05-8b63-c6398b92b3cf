C:\Users\<USER>\Desktop\work\flyXxtt2>docker-compose exec laravel.test composer install
Installing dependencies from lock file (including require-dev)
Verifying lock file contents can be installed on current platform.
Nothing to install, update or remove
Generating optimized autoload files
> Illuminate\Foundation\ComposerScripts::postAutoloadDump
> @php artisan package:discover --ansi

   INFO  Discovering packages.

  anourvalar/eloquent-serialize .......................................................... DONE
  blade-ui-kit/blade-heroicons ........................................................... DONE
  blade-ui-kit/blade-icons ............................................................... DONE
  filament/actions ....................................................................... DONE
  filament/filament ...................................................................... DONE
  filament/forms ......................................................................... DONE
  filament/infolists ..................................................................... DONE
  filament/notifications ................................................................. DONE
  filament/support ....................................................................... DONE
  filament/tables ........................................................................ DONE
  filament/widgets ....................................................................... DONE
  kirschbaum-development/eloquent-power-joins ............................................ DONE
  laravel-lang/actions ................................................................... DONE
  laravel-lang/attributes ................................................................ DONE
  laravel-lang/http-statuses ............................................................. DONE
  laravel-lang/lang ...................................................................... DONE
  laravel-lang/locales ................................................................... DONE
  laravel-lang/publisher ................................................................. DONE
  laravel/breeze ......................................................................... DONE
  laravel/horizon ........................................................................ DONE
  laravel/sail ........................................................................... DONE
  laravel/sanctum ........................................................................ DONE
  laravel/tinker ......................................................................... DONE
  livewire/livewire ...................................................................... DONE
  nesbot/carbon .......................................................................... DONE
  nunomaduro/collision ................................................................... DONE
  nunomaduro/termwind .................................................................... DONE
  ryangjchandler/blade-capture-directive ................................................. DONE
  spatie/laravel-ignition ................................................................ DONE
  spatie/laravel-medialibrary ............................................................ DONE
  srmklive/paypal ........................................................................ DONE

> @php artisan filament:upgrade
  ⇂ public/js/filament/forms/components/color-picker.js
  ⇂ public/js/filament/forms/components/date-time-picker.js
  ⇂ public/js/filament/forms/components/file-upload.js
  ⇂ public/js/filament/forms/components/key-value.js
  ⇂ public/js/filament/forms/components/markdown-editor.js
  ⇂ public/js/filament/forms/components/rich-editor.js
  ⇂ public/js/filament/forms/components/select.js
  ⇂ public/js/filament/forms/components/tags-input.js
  ⇂ public/js/filament/forms/components/textarea.js
  ⇂ public/js/filament/tables/components/table.js
  ⇂ public/js/filament/widgets/components/chart.js
t.js
  ⇂ public/js/filament/filament/app.js
  ⇂ public/js/filament/filament/echo.js
  ⇂ public/js/filament/notifications/notifications.js
  ⇂ public/js/filament/support/async-alpine.js
  ⇂ public/js/filament/support/support.js
  ⇂ public/css/filament/forms/forms.css
  ⇂ public/css/filament/support/support.css
  ⇂ public/css/filament/filament/app.css

   INFO  Successfully published assets!

   INFO  Configuration cache cleared successfully.

   INFO  Route cache cleared successfully.

   INFO  Compiled views cleared successfully.

   INFO  Successfully upgraded!

107 packages you are using are looking for funding.
Use the `composer fund` command to find out more!

C:\Users\<USER>\Desktop\work\flyXxtt2>docker-compose exec laravel.test php artisan key:generate

   INFO  Application key set successfully.


C:\Users\<USER>\Desktop\work\flyXxtt2>docker-compose exec laravel.test php artisan migrate

   INFO  Preparing database.

  Creating migration table .......................... 163ms DONE

   INFO  Running migrations.

  2014_10_12_000000_create_users_table .............. 275ms DONE
  2014_10_12_100000_create_password_reset_tokens_table  155ms DONE
  2019_08_19_000000_create_failed_jobs_table ........ 212ms DONE
  2019_12_14_000001_create_personal_access_tokens_table  202ms DONE
  2024_01_25_150822_create_donate_orders_table ...... 161ms DONE
  2024_02_05_003116_create_warehouses_table .......... 88ms DONE
  2024_07_20_132818_create_settings_table ........... 150ms DONE
  2024_08_03_003602_create_news_table ............... 142ms DONE
  2024_12_15_170844_create_tickets_table ............ 342ms DONE
  2024_12_15_170851_create_messages_table ........... 379ms DONE
  2025_01_21_185330_create_referrals_table ........... -9ms DONE
  2025_01_21_221530_create_referral_bonuses_table ... 619ms DONE
  2025_01_22_224546_add_referral_code_to_users ...... 285ms DONE
  2025_03_25_183649_create_email_changes_table ...... 162ms DONE
  2025_03_27_225956_create_starterpack_user_table ... 397ms DONE
  2025_05_06_193419_add_sync_mmoweb_to_users_table .. 206ms DONE


C:\Users\<USER>\Desktop\work\flyXxtt2>
