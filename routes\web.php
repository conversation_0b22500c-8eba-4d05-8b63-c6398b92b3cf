<?php

use App\Http\Controllers\DatabaseTestController;
use App\Http\Controllers\DonateController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\MediaController;
use App\Http\Controllers\MergeServerController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\StarterpacksController;
use App\Http\Controllers\StatisticController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\WarehouseController;
use Illuminate\Support\Facades\Route;

// 数据库连接测试路由
Route::get('/db-test', [DatabaseTestController::class, 'index'])->name('db-test');

Route::get('/stats', [StatisticController::class, 'list'])->name('stats');
Route::get('/stats/top-players', [StatisticController::class, 'top_players'])->name('top_players');
Route::get('/stats/rich', [StatisticController::class, 'top_rich'])->name('top_rich');
Route::get('/stats/pvp', [StatisticController::class, 'top_pvp'])->name('top_pvp');
Route::get('/stats/pk', [StatisticController::class, 'top_pk'])->name('top_pk');
Route::get('/stats/clans', [StatisticController::class, 'clans'])->name('clans');
Route::get('/stats/alliance', [StatisticController::class, 'ally'])->name('ally');
Route::get('/stats/castle', [StatisticController::class, 'castle'])->name('castle');
Route::get('/stats/clanhall', [StatisticController::class, 'clanhall'])->name('clanhall');
Route::get('/stats/epic-bosses', [StatisticController::class, 'epicBosses'])->name('epic-bosses');
Route::get('/stats/balance', [StatisticController::class, 'top_balance'])->name('top_balance');

//OTHER PAGES
Route::get('/x300_download', function () {
    return view('template::pages.x300_download');
})->name('x300_download');
Route::get('/x10_download', function () {
    return view('template::pages.x10_download');
})->name('x10_download');
Route::get('/x300_features', function () {
    return view('template::pages.x300_features');
})->name('x300_features');
Route::get('/x10_features', function () {
    return view('template::pages.x10_features');
})->name('x10_features');
Route::get('/essence_features', function () {
    return view('template::pages.essence_features');
})->name('essence_features');
Route::get('/faq', function () {
    return view('template::pages.faq');
})->name('faq');
Route::get('/privacy', function () {
    return view('template::pages.privacy');
})->name('privacy');
Route::get('/refund', function () {
    return view('template::pages.refund');
})->name('refund');
Route::get('/rules', function () {
    return view('template::pages.rules');
})->name('rules');
Route::get('/support', function () {
    return view('template::pages.support');
})->name('support');
Route::get('/terms', function () {
    return view('template::pages.terms');
})->name('terms');
Route::get('/agreement', function () {
    return view('template::pages.agreement');
})->name('agreement');
Route::get('/news', function () {
    return view('template::pages.news');
})->name('news');

Route::get('/language/{locale}', function ($locale) {
    // Проверяем, существует ли язык
    if (array_key_exists($locale, config('app.available_locales'))) {
        // Устанавливаем язык в сессию
        session()->put('locale', $locale);
        // Сохраняем язык в куки
        return redirect()->back()->withCookie(cookie()->forever('locale', $locale));
    }

    return redirect()->back();
});

Route::post('/switch-server', [HomeController::class, 'switchServer'])->name('switch-server');
Route::get('/', [HomeController::class, 'index'])->name('main-page');

Route::get('/download', function () {
    return view('template::pages.download');
})->name('download');

Route::get('/about', function () {
    return view('template::pages.about');
})->name('about');

Route::get('/features', function () {
    return view('template::pages.features');
})->name('features');

Route::get('/streams', function () {
    return view('template::dashboard.streamers');
})->name('streams');

// Webhook url от платежных систем
Route::post('/callback/freekassa', [DonateController::class, 'callbackFreekassa']);
Route::post('/callback/enot', [DonateController::class, 'callbackEnot']);
Route::post('/callback/morune', [DonateController::class, 'callbackMorune'])->name('callback-morune');
Route::post('/callback/primepayments', [DonateController::class, 'callbackPrimePayments']);
Route::post('/callback/paypal', [DonateController::class, 'callbackPaypal']);
Route::post('/callback/stripe', [DonateController::class, 'callbackStripe']);
Route::post('/callback/midtrans', [DonateController::class, 'callbackMidTrans']);

// Донат система для авторизованных и не авторизованных игроков
Route::get('/donate', [DonateController::class, 'index'])->name('donate');
Route::post('donate', [DonateController::class, 'store'])->name('donate.store');

Route::get('/bonus', [DonateController::class, 'bonusSystem'])->name('bonus');

Route::get('/dashboard', [ProfileController::class, 'index'])->middleware(['auth', 'verified'])->name('dashboard');

Route::get('/media', [MediaController::class, 'index'])->name('media');

Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{slug}', [NewsController::class, 'show'])->name('news.show');

Route::get('/referrals', [ReferralController::class, 'index'])->name('referral.index');
Route::post('/referral/update', [ReferralController::class, 'updateReferralCode'])->name('referral.update');

// Подтверждение нового email через ссылку
Route::get('/change-email/confirm/{token}', [ProfileController::class, 'confirmChangeEmail'])->name('profile.confirm-email');

Route::get('/email/verify', function () {
    return view('template::auth.verify-notice');
})->middleware('auth')->name('verify.notice');

Route::get('starterpacks', [StarterpacksController::class, 'index'])->name('starterpacks.index');
Route::post('starterpacks', [StarterpacksController::class, 'store'])->name('starterpacks.store');

Route::middleware('auth', 'verified')->group(function () {
    Route::get('/pay', [DonateController::class, 'auth_page'])->name('pay');
    Route::post('pay', [DonateController::class, 'createOrder'])->name('pay.store');

    Route::post('/unstuck', [ProfileController::class, 'unstuck'])->name('unstuck.store');
    Route::post('/hwid-delete', [ProfileController::class, 'deleteHwid'])->name('hwid.store');
    Route::post('/antiscam-delete', [ProfileController::class, 'deleteAntiscam'])->name('antiscam.store');

    Route::post('/create-game-account', [ProfileController::class, 'createGameAccount'])->name('game-account.store');
    Route::post('/change-game-password', [ProfileController::class, 'changeGamePassword'])->name('game-account.change-password');

    Route::get('/sync-game-accounts', [ProfileController::class, 'syncAccountIndex'])->name('sync-game-account');
    Route::post('/sync-game-account', [ProfileController::class, 'syncGameAccount'])->name('sync-game-account.store');

    Route::get('/warehouse', [WarehouseController::class, 'index'])->name('warehouse');
    Route::post('/send-to-game', [WarehouseController::class, 'sendCoinsInGame'])->name('warehouse.send-to-game');
    Route::post('/send-items', [WarehouseController::class, 'sendItems'])->name('send.items');

    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Показ формы смены email
    Route::get('/change-email', [ProfileController::class, 'changeEmailIndex'])->name('profile.change-email');

    // Обработка ввода нового email
    Route::post('/change-email', [ProfileController::class, 'changeEmail'])->name('profile.change-email-reset');

    Route::resource('tickets', TicketController::class)->except(['edit', 'update', 'destroy']);
    Route::post('tickets/{ticket}/messages', [MessageController::class, 'store'])->name('messages.store');
    Route::post('tickets/{ticket}/reply', [TicketController::class, 'reply'])->name('tickets.reply');
    Route::patch('/tickets/{ticket}/update-status', [TicketController::class, 'updateStatus'])->name('tickets.updateStatus');

    Route::get('/services', [ServiceController::class, 'index'])->name('service.index');
    Route::post('/service/change-name', [ServiceController::class, 'changeName'])->name('service.change-name');
    Route::post('/service/change-gender', [ServiceController::class, 'changeGender'])->name('service.change-gender');

});

require __DIR__.'/auth.php';
