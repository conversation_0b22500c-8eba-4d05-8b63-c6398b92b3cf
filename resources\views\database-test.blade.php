<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接测试 - XXTT2</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .connection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .connection-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .connection-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .connection-card.success {
            border-left: 4px solid #27ae60;
            background: #f8fff9;
        }
        
        .connection-card.error {
            border-left: 4px solid #e74c3c;
            background: #fff8f8;
        }
        
        .connection-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .connection-description {
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            margin-bottom: 15px;
        }
        
        .status-success {
            background: #27ae60;
            color: white;
        }
        
        .status-error {
            background: #e74c3c;
            color: white;
        }
        
        .connection-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #6c757d;
            word-break: break-all;
        }
        
        .stats-section {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #ecf0f1;
        }
        
        .stats-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stats-card h3 {
            margin: 0 0 15px 0;
            font-size: 1.2em;
        }
        
        .stats-item {
            margin-bottom: 10px;
            font-size: 0.95em;
        }
        
        .stats-number {
            font-weight: bold;
            font-size: 1.3em;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1em;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
            transition: all 0.3s;
        }
        
        .refresh-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }
        
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            margin-top: 20px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 数据库连接测试</h1>
            <p>XXTT2 游戏管理系统 - 实时数据库状态监控</p>
        </div>
        
        <div class="content">
            <div class="connection-grid">
                @foreach($connections as $connection)
                <div class="connection-card {{ $connection['status'] }}">
                    <div class="connection-title">{{ $connection['description'] }}</div>
                    <div class="connection-description">连接名: {{ $connection['name'] }}</div>
                    
                    <span class="status-badge status-{{ $connection['status'] }}">
                        @if($connection['status'] === 'success')
                            ✅ 连接成功
                        @else
                            ❌ 连接失败
                        @endif
                    </span>
                    
                    <div><strong>状态信息:</strong> {{ $connection['message'] }}</div>
                    
                    @if($connection['info'])
                    <div class="connection-info">
                        <strong>连接详情:</strong>
                        @foreach($connection['info'] as $key => $value)
                        <div class="info-item">
                            <span class="info-label">{{ ucfirst($key) }}:</span>
                            <span class="info-value">{{ $value }}</span>
                        </div>
                        @endforeach
                    </div>
                    @endif
                    
                    @if($connection['test_result'])
                    <div style="margin-top: 10px;">
                        <strong>测试结果:</strong> {{ $connection['test_result'] }}
                    </div>
                    @endif
                </div>
                @endforeach
            </div>
            
            <div class="stats-section">
                <h2 class="stats-title">📊 数据库统计信息</h2>
                <div class="stats-grid">
                    @if(isset($stats['cms']))
                    <div class="stats-card">
                        <h3>🌐 Web CMS 数据</h3>
                        @if(isset($stats['cms']['error']))
                            <div style="color: #ffcccb;">错误: {{ $stats['cms']['error'] }}</div>
                        @else
                            <div class="stats-item">
                                用户数量: <span class="stats-number">{{ $stats['cms']['users_count'] ?? 0 }}</span>
                            </div>
                            <div class="stats-item">
                                新闻数量: <span class="stats-number">{{ $stats['cms']['news_count'] ?? 0 }}</span>
                            </div>
                            <div class="stats-item">
                                订单数量: <span class="stats-number">{{ $stats['cms']['orders_count'] ?? 0 }}</span>
                            </div>
                        @endif
                    </div>
                    @endif
                    
                    @if(isset($stats['game']))
                    <div class="stats-card">
                        <h3>🎮 游戏数据</h3>
                        @if(isset($stats['game']['error']))
                            <div style="color: #ffcccb;">错误: {{ $stats['game']['error'] }}</div>
                        @else
                            <div class="stats-item">
                                角色数量: <span class="stats-number">{{ $stats['game']['characters_count'] ?? 0 }}</span>
                            </div>
                            <div class="stats-item">
                                账户数量: <span class="stats-number">{{ $stats['game']['accounts_count'] ?? 0 }}</span>
                            </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
            
            <div class="timestamp">
                最后更新时间: {{ now()->format('Y-m-d H:i:s') }}
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="window.location.reload()">
        🔄 刷新
    </button>
</body>
</html>
